"use client"

import * as React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  closestCenter,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core"
import { restrictToVerticalAxis } from "@dnd-kit/modifiers"
import {
  SortableContext,
  arrayMove,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import {
  ChevronDown,
  ChevronRight,
  GripVertical,
  Bot,
  Plus,
  Sparkles,
  ArrowUp,
  ArrowDown,
  Trash2,
  Languages
} from "lucide-react"
import { cn } from "@/lib/utils"
import { NewsletterBlock, NewsletterHeaderFooter } from "@/types/newsletter"
import { Block } from "@/types/block"
import { HeaderFooterItem } from "./header-footer-item"
import { AddBlockModal } from "../modals/add-block-modal"
import { useLanguageContext } from "@/contexts/language-context"
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog"

interface BlockAccordionProps {
  blocks: NewsletterBlock[]
  headers?: NewsletterHeaderFooter[]
  footers?: NewsletterHeaderFooter[]
  onBlocksReorder: (blocks: NewsletterBlock[]) => void
  onBlockAIClick: (block: NewsletterBlock) => void
  onBlockTranslateClick?: (block: NewsletterBlock) => void
  onDeleteBlock?: (blockId: string) => void
  onHeadersReorder?: (headers: NewsletterHeaderFooter[]) => void
  onFootersReorder?: (footers: NewsletterHeaderFooter[]) => void
  onHeaderFooterAIClick?: (headerFooter: NewsletterHeaderFooter, type: 'header' | 'footer') => void
  onHeaderFooterTranslateClick?: (headerFooter: NewsletterHeaderFooter, type: 'header' | 'footer') => void
  onAddBlock?: (block: Block) => void
  onVariableChange?: (blockId: string, variableId: string, language: string, value: string) => void
  onHtmlContentChange?: (blockId: string, htmlContent: string) => void
  onHeaderFooterVariableChange?: (headerFooterId: string, variableId: string, language: string, value: string) => void
  onHeaderFooterHtmlContentChange?: (headerFooterId: string, htmlContent: string) => void
  brandName?: string
  brand?: string
}

interface SortableBlockItemProps {
  block: NewsletterBlock
  isExpanded: boolean
  onToggle: () => void
  onAIClick: () => void
  onTranslateClick?: () => void
  onDeleteBlock?: (blockId: string) => void
  onVariableChange?: (blockId: string, variableId: string, language: string, value: string) => void
  onHtmlContentChange?: (blockId: string, htmlContent: string) => void
  isDragging?: boolean
  isDragDisabled?: boolean
}

interface SortableHeaderFooterItemProps {
  headerFooter: NewsletterHeaderFooter
  type: 'header' | 'footer'
  isExpanded: boolean
  onToggle: () => void
  onAIClick: () => void
  onTranslateClick?: () => void
  onVariableChange?: (headerFooterId: string, variableId: string, language: string, value: string) => void
  onHtmlContentChange?: (headerFooterId: string, htmlContent: string) => void
  isDragging?: boolean
  isDragDisabled?: boolean
}

function SortableBlockItem({
  block,
  isExpanded,
  onToggle,
  onAIClick,
  onTranslateClick,
  onDeleteBlock,
  onVariableChange,
  onHtmlContentChange,
  isDragging,
  isDragDisabled = false
}: SortableBlockItemProps) {
  const { selectedLanguage } = useLanguageContext()
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: sortableIsDragging,
  } = useSortable({ id: block.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const isCurrentlyDragging = isDragging || sortableIsDragging

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "relative",
        isCurrentlyDragging && "z-10 opacity-80"
      )}
    >
      <Card className={cn("transition-colors", !block.is_visible && "opacity-60")}>
        <Collapsible open={isExpanded}>
          <CollapsibleTrigger asChild>
            <CardHeader
              className="cursor-pointer hover:bg-muted/50 transition-colors"
              onClick={onToggle}
            >
              <div className="flex items-center gap-3">
                {/* Drag Handle */}
                <Button
                  {...(isDragDisabled ? {} : attributes)}
                  {...(isDragDisabled ? {} : listeners)}
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-6 w-6",
                    isDragDisabled
                      ? "cursor-not-allowed"
                      : "cursor-grab active:cursor-grabbing"
                  )}
                  onClick={(e) => e.stopPropagation()}
                  disabled={isDragDisabled}
                >
                  <GripVertical className={cn(
                    "h-4 w-4",
                    isDragDisabled
                      ? "text-muted-foreground/40"
                      : "text-black"
                  )} />
                </Button>

                {/* Expand/Collapse Icon */}
                <div className="flex-shrink-0">
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>

                {/* Block Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-sm truncate">{block.name}</CardTitle>
                    <div className="flex items-center gap-1">
                      <Badge variant="outline" className="text-xs">
                        #{block.order_position}
                      </Badge>
                    </div>
                  </div>
                  <CardDescription className="text-xs truncate">
                    {block.description || "Sense descripció"}
                  </CardDescription>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center gap-2">
                  {/* Translate Button */}
                  <Button
                    variant="outline"
                    size="icon"
                    className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-200 hover:border-blue-300"
                    onClick={(e) => {
                      e.stopPropagation()
                      onTranslateClick?.()
                    }}
                  >
                    <Languages className="h-4 w-4" />
                  </Button>

                  {/* AI Button */}
                  <Button
                    variant="outline"
                    size="icon"
                    className="flex shadow-lg text-yellow-400  transition-all duration-300 hover:bg-gradient-to-r hover:from-purple-500 hover:to-blue-500 hover:shadow-xl hover:shadow-blue-500/30 hover:scale-110 group animate-pulse-gradient hover:text-white"
                    onClick={(e) => {
                      e.stopPropagation()
                      onAIClick()
                    }}
                  >
                    <Sparkles className="h-6 w-6 group-hover:animate-pulse" />
                  </Button>
                </div>
              </div>
            </CardHeader>
          </CollapsibleTrigger>

          <CollapsibleContent>
            <CardContent className="pt-0">
              <Separator className="mb-4" />

              {/* Block Actions */}
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-sm font-medium">Block Actions</h4>
                {onDeleteBlock && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowDeleteDialog(true)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 hover:border-red-300"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Block
                  </Button>
                )}
              </div>

              {/* Block Details */}
              <div className="space-y-3">
                <div>
                  <h4 className="text-sm font-medium mb-2">Variables</h4>
                  {block.variable_values && block.variable_values.length > 0 ? (
                    <div className="space-y-4">
                      {block.variable_values.map((variable) => (
                        <div key={variable.id} className="border rounded-lg p-3">
                          <div className="font-medium text-sm mb-2">
                            {variable.name} ({variable.variable_type_display_name})
                          </div>
                          <Tabs value={selectedLanguage} className="w-full">
                            <TabsList className="grid w-full grid-cols-4">
                              <TabsTrigger value="es">ES</TabsTrigger>
                              <TabsTrigger value="ca">CA</TabsTrigger>
                              <TabsTrigger value="fr">FR</TabsTrigger>
                              <TabsTrigger value="en">EN</TabsTrigger>
                            </TabsList>
                            {Object.entries(variable.value).map(([lang, value]) => (
                              <TabsContent key={lang} value={lang} className="mt-2">
                                <div className="space-y-1">
                                  <Label htmlFor={`${variable.id}-${lang}`} className="text-xs">
                                    {lang.toUpperCase()}
                                  </Label>
                                  {variable.variable_type_field_type === 'text' ? (
                                    <Textarea
                                      id={`${variable.id}-${lang}`}
                                      value={value || ''}
                                      onChange={(e) => onVariableChange?.(block.id, variable.id, lang, e.target.value)}
                                      className="min-h-[60px] text-xs"
                                      placeholder={`Introdueix ${variable.variable_type_display_name} en ${lang.toUpperCase()}`}
                                    />
                                  ) : (
                                    <Input
                                      id={`${variable.id}-${lang}`}
                                      value={value || ''}
                                      onChange={(e) => onVariableChange?.(block.id, variable.id, lang, e.target.value)}
                                      className="text-xs"
                                      placeholder={`Introdueix ${variable.variable_type_display_name} en ${lang.toUpperCase()}`}
                                    />
                                  )}
                                </div>
                              </TabsContent>
                            ))}
                          </Tabs>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-xs text-muted-foreground">No s'han definit variables</p>
                  )}
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">Contingut HTML</h4>
                  <div className="space-y-2">
                    <Textarea
                      value={block.html_content || ''}
                      onChange={(e) => onHtmlContentChange?.(block.id, e.target.value)}
                      className="min-h-[120px] font-mono text-xs"
                      placeholder="Introdueix el contingut HTML per a aquest bloc"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={() => onDeleteBlock?.(block.id)}
        title="Delete Block"
        itemName={block.name}
        itemType="block"
        description={`Are you sure you want to delete the block "${block.name}"? This action cannot be undone and will remove all content and variables associated with this block.`}
      />
    </div>
  )
}

function SortableHeaderFooterItem({
  headerFooter,
  type,
  isExpanded,
  onToggle,
  onAIClick,
  onTranslateClick,
  onVariableChange,
  onHtmlContentChange,
  isDragging,
  isDragDisabled = false
}: SortableHeaderFooterItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: sortableIsDragging,
  } = useSortable({ id: headerFooter.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const isCurrentlyDragging = isDragging || sortableIsDragging

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "relative",
        isCurrentlyDragging && "z-10 opacity-80",
        isDragDisabled && "cursor-not-allowed"
      )}
      {...(isDragDisabled ? {} : attributes)}
      {...(isDragDisabled ? {} : listeners)}
    >
      <HeaderFooterItem
        headerFooter={headerFooter}
        type={type}
        isExpanded={isExpanded}
        onToggle={onToggle}
        onAIClick={onAIClick}
        onTranslateClick={onTranslateClick}
        onVariableChange={onVariableChange}
        onHtmlContentChange={onHtmlContentChange}
        isDragging={isCurrentlyDragging}
        isDragDisabled={isDragDisabled}
      />
    </div>
  )
}

export function BlockAccordion({
  blocks,
  headers = [],
  footers = [],
  onBlocksReorder,
  onBlockAIClick,
  onBlockTranslateClick,
  onDeleteBlock,
  onHeadersReorder,
  onFootersReorder,
  onHeaderFooterAIClick,
  onHeaderFooterTranslateClick,
  onAddBlock,
  onVariableChange,
  onHtmlContentChange,
  onHeaderFooterVariableChange,
  onHeaderFooterHtmlContentChange,
  brandName,
  brand
}: BlockAccordionProps) {
  const [expandedBlocks, setExpandedBlocks] = useState<Set<string>>(new Set())
  const [expandedHeaders, setExpandedHeaders] = useState<Set<string>>(new Set())
  const [expandedFooters, setExpandedFooters] = useState<Set<string>>(new Set())
  const [isDragging, setIsDragging] = useState(false)
  const [addBlockModalOpen, setAddBlockModalOpen] = useState(false)

  // Check if any block is expanded (open)
  const hasAnyBlockOpen = expandedBlocks.size > 0 || expandedHeaders.size > 0 || expandedFooters.size > 0
  const isDragDisabled = hasAnyBlockOpen

  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: { distance: 5 },
      // Disable sensor when any block is open
      disabled: isDragDisabled
    }),
    useSensor(TouchSensor, {
      // Disable sensor when any block is open
      // disabled: isDragDisabled
    }),
    useSensor(KeyboardSensor, {
      // Disable sensor when any block is open
      // disabled: isDragDisabled
    })
  )

  const handleDragStart = () => {
    setIsDragging(true)
    // Collapse all blocks during drag
    setExpandedBlocks(new Set())
  }

  const handleDragEnd = (event: DragEndEvent) => {
    setIsDragging(false)

    const { active, over } = event
    if (!active || !over) return

    const activeId = active.id as string
    const overId = over.id as string

    if (activeId !== overId) {
      // Check if we're reordering blocks
      const blockOldIndex = blocks.findIndex(block => block.id === activeId)
      const blockNewIndex = blocks.findIndex(block => block.id === overId)

      if (blockOldIndex !== -1 && blockNewIndex !== -1) {
        try {
          const reorderedBlocks = arrayMove(blocks, blockOldIndex, blockNewIndex)
          // Update order positions
          const updatedBlocks = reorderedBlocks.map((block, index) => ({
            ...block,
            order_position: index + 1
          }))
          onBlocksReorder(updatedBlocks)
        } catch (error) {
          console.error('Error reordering blocks:', error)
          setIsDragging(false)
        }
        return
      }

      // Check if we're reordering headers
      const headerOldIndex = headers.findIndex(header => header.id === activeId)
      const headerNewIndex = headers.findIndex(header => header.id === overId)

      if (headerOldIndex !== -1 && headerNewIndex !== -1) {
        try {
          const reorderedHeaders = arrayMove(headers, headerOldIndex, headerNewIndex)
          onHeadersReorder?.(reorderedHeaders)
        } catch (error) {
          console.error('Error reordering headers:', error)
          setIsDragging(false)
        }
        return
      }

      // Check if we're reordering footers
      const footerOldIndex = footers.findIndex(footer => footer.id === activeId)
      const footerNewIndex = footers.findIndex(footer => footer.id === overId)

      if (footerOldIndex !== -1 && footerNewIndex !== -1) {
        try {
          const reorderedFooters = arrayMove(footers, footerOldIndex, footerNewIndex)
          onFootersReorder?.(reorderedFooters)
        } catch (error) {
          console.error('Error reordering footers:', error)
          setIsDragging(false)
        }
        return
      }
    }
  }

  const toggleBlockExpansion = (blockId: string) => {
    if (isDragging) return // Don't allow expansion during drag

    setExpandedBlocks(prev => {
      const newSet = new Set(prev)
      if (newSet.has(blockId)) {
        newSet.delete(blockId)
      } else {
        newSet.add(blockId)
      }
      return newSet
    })
  }

  const toggleHeaderExpansion = (headerId: string) => {
    setExpandedHeaders(prev => {
      const newSet = new Set(prev)
      if (newSet.has(headerId)) {
        newSet.delete(headerId)
      } else {
        newSet.add(headerId)
      }
      return newSet
    })
  }

  const toggleFooterExpansion = (footerId: string) => {
    setExpandedFooters(prev => {
      const newSet = new Set(prev)
      if (newSet.has(footerId)) {
        newSet.delete(footerId)
      } else {
        newSet.add(footerId)
      }
      return newSet
    })
  }

  const handleKeyboardToggle = (blockId: string, event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      toggleBlockExpansion(blockId)
    }
  }

  const handleBlockSelect = (block: Block) => {
    onAddBlock?.(block)
  }

  const sortedBlocks = (blocks || []).slice().sort((a, b) => a.order_position - b.order_position)

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Contingut de la newsletter</h3>
          <p className="text-sm text-muted-foreground">
            {headers.length} capçaler{headers.length !== 1 ? 'es' : 'a'}, {blocks.length} bloc{blocks.length !== 1 ? 's' : ''}, {footers.length} peu{footers.length !== 1 ? 's' : ''} de pàgina
            {brandName && ` per a ${brandName}`}
          </p>
        </div>
        <div className="flex gap-2">
          {onAddBlock && (
            <Button variant="outline" size="sm" onClick={() => setAddBlockModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Afegir bloc
            </Button>
          )}
        </div>
      </div>

      <div className="space-y-4">
        {/* Headers Section */}
        {headers.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <ArrowUp className="h-4 w-4" />
              Capçaleres
            </h4>
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              modifiers={[restrictToVerticalAxis]}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
            >
              <SortableContext items={headers.map(header => header.id)} strategy={verticalListSortingStrategy}>
                <div className="space-y-2">
                  {headers.map((header) => (
                    <SortableHeaderFooterItem
                      key={header.id}
                      headerFooter={header}
                      type="header"
                      isExpanded={expandedHeaders.has(header.id)}
                      onToggle={() => toggleHeaderExpansion(header.id)}
                      onAIClick={() => onHeaderFooterAIClick?.(header, 'header')}
                      onTranslateClick={onHeaderFooterTranslateClick ? () => onHeaderFooterTranslateClick(header, 'header') : undefined}
                      onVariableChange={onHeaderFooterVariableChange}
                      onHtmlContentChange={onHeaderFooterHtmlContentChange}
                      isDragging={isDragging}
                      isDragDisabled={isDragDisabled}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          </div>
        )}

        {/* Blocks Section */}
        <div className="space-y-2">
          {blocks.length > 0 && (
            <h4 className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Bot className="h-4 w-4" />
              Blocs de contingut
            </h4>
          )}

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            modifiers={[restrictToVerticalAxis]}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={sortedBlocks.map(block => block.id)} strategy={verticalListSortingStrategy}>
              <div className="space-y-2">
                {sortedBlocks.map((block) => (
                  <SortableBlockItem
                    key={block.id}
                    block={block}
                    isExpanded={expandedBlocks.has(block.id)}
                    onToggle={() => toggleBlockExpansion(block.id)}
                    onAIClick={() => onBlockAIClick(block)}
                    onTranslateClick={onBlockTranslateClick ? () => onBlockTranslateClick(block) : undefined}
                    onDeleteBlock={onDeleteBlock}
                    onVariableChange={onVariableChange}
                    onHtmlContentChange={onHtmlContentChange}
                    isDragging={isDragging}
                    isDragDisabled={isDragDisabled}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        </div>

        {/* Footers Section */}
        {footers.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <ArrowDown className="h-4 w-4" />
              Peus de pàgina
            </h4>
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              modifiers={[restrictToVerticalAxis]}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
            >
              <SortableContext items={footers.map(footer => footer.id)} strategy={verticalListSortingStrategy}>
                <div className="space-y-2">
                  {footers.map((footer) => (
                    <SortableHeaderFooterItem
                      key={footer.id}
                      headerFooter={footer}
                      type="footer"
                      isExpanded={expandedFooters.has(footer.id)}
                      onToggle={() => toggleFooterExpansion(footer.id)}
                      onAIClick={() => onHeaderFooterAIClick?.(footer, 'footer')}
                      onTranslateClick={onHeaderFooterTranslateClick ? () => onHeaderFooterTranslateClick(footer, 'footer') : undefined}
                      onVariableChange={onHeaderFooterVariableChange}
                      onHtmlContentChange={onHeaderFooterHtmlContentChange}
                      isDragging={isDragging}
                      isDragDisabled={isDragDisabled}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          </div>
        )}
      </div>

      {/* Add Block Modal */}
      <AddBlockModal
        open={addBlockModalOpen}
        onOpenChange={setAddBlockModalOpen}
        brand={brand}
        onBlockSelect={handleBlockSelect}
      />
    </div>
  )
}
