server {
    # listen 443 ssl;
    listen 80;
    server_name pre.newsletter.grandvaliraresorts.com;

    # ssl_certificate /etc/nginx/ssl/grandvaliraresorts/fullchain.pem;
    # ssl_certificate_key /etc/nginx/ssl/grandvaliraresorts/grandvaliraresorts.key;

    access_log /var/log/nginx/pre.access.log;
    error_log /var/log/nginx/pre.error.log;

    root /var/www/grandvalira-newsletter-front-pre/actions-runner/grandvalira-newsletter-front-pre/grandvalira-newsletter-front/grandvalira-newsletter-front;

    # Increase buffer sizes
    proxy_buffer_size 512k;
    proxy_buffers 4 512k;
    proxy_busy_buffers_size 1024k;

    # Increase header buffer size
    large_client_header_buffers 8 64k;   # 8 buffers of 64KB each
    # client_header_buffer_size 64k;   # Increased to 64k
    # client_max_body_size 50M;

    # Django API
    location ^~ /apiv1/ {
        proxy_pass http://127.0.0.1:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_connect_timeout 120s;
        proxy_read_timeout 120s;
        proxy_send_timeout 120s;

        # Remove NextAuth cookies from requests to API
        proxy_set_header Cookie "";
    }

    # Django Static files
    location /static/ {
        alias /var/www/grandvalira-newsletters-back/staticfiles/;
        autoindex on;
    }

    # Django Media files
    location /media/ {
        alias /var/www/grandvalira-newsletters-back/media/;
        autoindex on;
    }

    # Next.js Static files
    location /_next/static/ {
        alias /var/www/grandvalira-newsletter-front-pro/_work/grandvalira-newsletter-front/grandvalira-newsletter-front/.next/static/;
        expires max;
        add_header Cache-Control "public, immutable";
        proxy_connect_timeout 120s;
        proxy_read_timeout 120s;
        proxy_send_timeout 120s;
    }

    # Next.js Root
    location / {
        try_files $uri.html $uri/index.html @nextjs;
        proxy_connect_timeout 120s;
        proxy_read_timeout 120s;
        proxy_send_timeout 120s;
    }

    # Next.js Proxy
    location @nextjs {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;

        proxy_connect_timeout 120s;
        proxy_read_timeout 120s;
        proxy_send_timeout 120s;
    }
}

# server {
#     listen 80;
#     server_name pre.newsletter.grandvaliraresorts.com;
#     return 301 https://$host$request_uri;
# }