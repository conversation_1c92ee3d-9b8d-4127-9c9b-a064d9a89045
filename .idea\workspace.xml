<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="24ab36d9-7c85-497f-a7ab-337c510de7af" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/grandvalira-newsletter-front.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/inspectionProfiles/Project_Default.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/demo/page.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/app/demo/page.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/sidebar/auto-breadcrumbs.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/components/sidebar/auto-breadcrumbs.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/components/table/master-table-example.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/table/master-table.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/components/users/user-management.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/components/users/user-management.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FormatOnSaveOptions">
    <option name="myRunOnSave" value="true" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;FernandoInfini&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/InfiniWorkspace/grandvalira-newsletter-front.git&quot;,
    &quot;accountId&quot;: &quot;9588f11b-3f75-4c14-b54e-f443c5647b0c&quot;
  }
}</component>
  <component name="OptimizeOnSaveOptions">
    <option name="myRunOnSave" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="3276q1996RdFdDJuKW6H2jVESUm" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "git-widget-placeholder": "dev",
    "ignore.virus.scanning.warn.message": "true",
    "js.debugger.nextJs.config.created.client": "true",
    "js.debugger.nextJs.config.created.server": "true",
    "last_opened_file_path": "C:/Users/<USER>/Projects/grandvalira-newsletter-front",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.Next.js: server-side.executor": "Run",
    "settings.editor.selected.configurable": "actions.on.save",
    "ts.external.directory.path": "C:\\Users\\<USER>\\Projects\\grandvalira-newsletter-front\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="npm.Next.js: server-side">
    <configuration name="Next.js: debug client-side" type="JavascriptDebugType" uri="http://localhost:3000/">
      <method v="2" />
    </configuration>
    <configuration name="Next.js: server-side" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-WS-251.26927.40" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="24ab36d9-7c85-497f-a7ab-337c510de7af" name="Changes" comment="" />
      <created>1756759452312</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756759452312</updated>
      <workItem from="1756759454906" duration="46000" />
      <workItem from="1756797704450" duration="2256000" />
      <workItem from="1756800011315" duration="1417000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>