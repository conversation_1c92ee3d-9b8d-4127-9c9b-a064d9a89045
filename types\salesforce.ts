// Salesforce folder types
export interface SalesforceFolder {
  id: string
  salesforce_id: number
  name: string
  description: string
  level: number
  full_path: string
  is_root: boolean
  has_children: boolean
  children?: SalesforceFolder[]
}

export interface SalesforceFoldersResponse {
  success: boolean
  format: string
  folders: SalesforceFolder[]
}

export interface CreateSalesforceFolderRequest {
  name: string
  description: string
  parent_id: number
}

export interface CreateSalesforceFolderResponse {
  success: boolean
  folder: SalesforceFolder
}

// Flattened folder for easier selection handling
export interface FlatSalesforceFolder {
  id: string
  salesforce_id: number
  name: string
  description: string
  level: number
  full_path: string
  is_root: boolean
  has_children: boolean
}
