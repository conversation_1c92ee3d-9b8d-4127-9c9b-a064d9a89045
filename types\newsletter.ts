import { ReactNode } from "react"

export interface NewsletterLanguage {
  id: string
  language: string
  language_display: string
  name: string
  updated_in_salesforce: string | null
  created_in_salesforce: boolean
  salesforce_id: string | null
}

export interface NewsletterUser {
  id: string
  first_name: string
  last_name: string
  email: string
  updated_at_platform: string
  created_at_platform: string
}

export interface Newsletter {
  id: string
  name: string
  brand: string
  brand_name: string
  template: string
  template_name: string
  status: string
  status_display: string
  languages: NewsletterLanguage[]
  created_by?: NewsletterUser
  updated_by?: NewsletterUser
  created_at?: string
  updated_at?: string
}

export interface NewsletterResponse {
  count: number
  next: string | null
  previous: string | null
  results: Newsletter[]
}

export interface NewsletterTableItem {
  id: string
  header: string
  type: string
  status: string
  target: string
  limit: string
  reviewer: string
}

// Newsletter creation types
export interface CreateNewsletterRequest {
  name: string
  template: string
  languages: string[]
  salesforce_folder?: string // Optional UUID of the selected Salesforce folder
}

export interface CreateNewsletterResponse {
  id: string
  name: string
  brand: string
  brand_name: string
  template: string
  template_name: string
  status: string
  status_display: string
  languages: NewsletterLanguage[]
  created_by?: NewsletterUser
  updated_by?: NewsletterUser
  created_at?: string
  updated_at?: string
}

// Newsletter Builder Types
export interface NewsletterBlockVariable {
  id: string
  variable_type_id: string
  variable_type_name: string
  variable_type_display_name: string
  variable_type_field_type: string
  name: string
  value: {
    es: string
    ca: string
    fr: string
    en: string
  }
}

export interface NewsletterBlock {
  element_type_display: ReactNode
  id: string
  name: string
  description: string | null
  html_content: string
  order_position: number
  is_visible: boolean
  variable_values: NewsletterBlockVariable[]
  last_edited_by: any | null
  last_edited_at: string
  created_at: string
  updated_at: string
}

export interface NewsletterHeaderFooter {
  id: string
  header_footer: string
  name: string
  html_content: string
  element_type_display: string
  variable_values: NewsletterBlockVariable[]
  edited_by: any | null
  created_at: string
  updated_at: string
}

export interface NewsletterUser {
  id: string
  first_name: string
  last_name: string
  email: string
  updated_at_platform: string
  created_at_platform: string
}

export interface NewsletterBuilderData {
  id: string
  name: string
  brand: string
  brand_name: string
  template: string
  template_name: string
  status: string
  status_display: string
  languages: NewsletterLanguage[]
  nl_blocks: NewsletterBlock[]
  headers: NewsletterHeaderFooter[]
  footers: NewsletterHeaderFooter[]
  created_by: NewsletterUser
  updated_by: NewsletterUser
  created_at: string
  updated_at: string
}

export interface UpdateNewsletterRequest {
  newsletter_parent_id: string
  name: string
  status: string
  nl_blocks: NewsletterBlock[]
  headers?: NewsletterHeaderFooter[]
  footers?: NewsletterHeaderFooter[]
}
